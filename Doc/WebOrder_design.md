## Data structure

In restaurant.graphql, the data structure is as follows:
Restaurant -> categories -> foods -> variations -> addons -> options.
Addons are the options that can be added to the food.

## Requirements

read .json and restaurantid.json

### Menu display:

The webpage should show 3 main parts:

1. header, which has the restaurant name and logo
2. menu categories, which are the categories of foods, and in each category, there are foods as in data/restaurantid.json
   1. there are + button for each food, which will add the food to the cart.
   2. When click the + button, a modal will pop up, which shows the variations of the food and the addons. Customers can select the variation and addons, and then click the add to cart button to add the food to the cart. Depends on the minumum and maximum quantity of the addon, it should display radio box or check box
   3. When checked, the price will be added to the price of the food. and the summary will be updated in the bottom of the modal page.
   4. When click the add to cart button, the food will be added to the cart, and the modal will be closed. The card should be updated for the new food with the price and quantity, and variation, addons.
   5. When click the - button, the food will be removed from the cart, and the card should be updated.
   6. When click the checkout button, the order will be sent to the server, and the card should be cleared. The order structure should use mutation placeOrder of order.graphql.
   7. When click the cancel button, the card should be cleared.
3. cart, which is float in the right bottom of the page. shows the foods in the cart, and the price of each food, and the total price, and a checkout button.

Current UI layout is correct, don't touch that part, but adjust menu display, add modal, add cart, add checkout button, add cancel button according to the datastructure.

### security and session management

The customer will get a link with a unique token, which will be used to identify the customer. The token will be stored in the browser, and will be sent to the server when the customer makes an order. The token will be used to identify the customer when the customer makes an order.

[删除旧的 generateMenuLink 代码段]

extend type Mutation {
placeOrder(
restaurantId: ID!
customerId: String!
orderInput: [OrderInput!]!
paymentMethod: String!
couponCode: String
deliveryAddressId: ID
tipping: Float
orderDate: String!
isPickedUp: Boolean!
taxationAmount: Float!
deliveryCharges: Float!
instructions: String
): Order!

input AddonsInput {
\_id: String
options: [String!]
}
input OrderInput {
food: String!
quantity: Int!
variation: String!
addons: [AddonsInput!]
specialInstructions: String
}
