# ============================================================================
# Order Summary Types
# ============================================================================

type OrderBrief {
  orderId: ID!
  amount: Float!
  restaurantId: ID!
  restaurantName: String!
  restaurantBrandId: String!
  restaurantBrand: String!
  orderStatus: OrderStatus!
  orderDate: DateTime!
}

# ============================================================================
# Order Status Enum
# ============================================================================

enum OrderStatus {
  PENDING
  ACCEPTED
  PICKED
  DELIVERED
  CANCELLED
  COMPLETED
  ASSIGNED
}

# ============================================================================
# Detailed Order Type
# ============================================================================

type Order {
  _id: ID!
  """
  same as in orderbrief
  """
  orderId: String!
  orderAmount: Float
  restaurantId: ID!
  restaurantName: String!
  restaurantBrandId: ID
  restaurantBrand: String
  orderStatus: OrderStatus!
  orderDate: DateTime!
  """
  detailed order info
  """
  customerId: String!
  items: [Item!]!
  """
  # deliveryAddress is formattedAddress in CustomerAddress, for print and manual check. Also to ensure the data is saved even if customer delete their address info.
  # deliveryAddressId is addressId in CustomerAddress, together with customerId could identify the unique address
  """
  isPickedUp: Boolean!
  deliveryAddress: String!
  deliveryAddressId: ID!
  deliveryCoordinates: Point
  deliveryInstructions: String
  """
  status
  """
  status: Boolean
  completionTime: String
  expectedTime: String
  preparationTime: String
  acceptedAt: String
  pickedAt: String
  deliveredAt: String
  cancelledAt: String
  assignedAt: String

  """
  payment related
  """
  paymentMethod: String
  paidAmount: Float
  paymentStatus: String!
  reason: String
  isActive: Boolean!
  createdAt: String!
  updatedAt: String!
  deliveryCharges: Float
  tipping: Float!
  taxationAmount: Float!
  """
  delivery related
  """
  rider: Rider
  review: Review
  zone: Zone!
  isRinged: Boolean!
  isRiderRinged: Boolean!
  instructions: String
}

extend type Query {
  orders(
    restaurantId: ID
    restaurantBrandId: ID
    orderStatus: OrderStatus
    startTime: DateTime
    endTime: DateTime
    offset: Int
  ): [Order!]!
  order(id: String!): Order!
  ordersByRestId(
    restaurant: String!
    page: Int
    rows: Int
    search: String
  ): [Order!]
    getOrdersByDateRange(
    startingDate: String!
    endingDate: String!
    restaurant: String!
  ): OrdersWithCashOnDeliveryInfo!
    getOrderStatuses: [String!]
  getPaymentStatuses: [String!]
  undeliveredOrders(offset: Int): [Order!]!
  deliveredOrders(offset: Int): [Order!]!
  allOrders(page: Int): [Order!]!
}

extend type Mutation {
  placeOrder(
    restaurantId: ID!
    customerId: String!
    orderInput: [OrderInput!]!
    paymentMethod: String!
    couponCode: String
    deliveryAddressId: ID
    tipping: Float
    orderDate: String!
    isPickedUp: Boolean!
    taxationAmount: Float!
    deliveryCharges: Float!
    instructions: String
  ): Order!

  cancelOrder(_id: String!, reason: String!): Order!
  editOrder(_id: String!, orderInput: [OrderInput!]!): Order!
  acceptOrder(_id: String!, time: String): Order!
}
extend type Subscription {
  subscribePlaceOrder(restaurant: String!): SubscriptionOrders!
  SubscribeOrderStatus(userId: String!): SubscriptionOrders!
  subscriptionOrder(id: String!): Order!
  subscriptionAssignRider(riderId: String!): SubscriptionOrders!
}
