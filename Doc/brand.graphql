type Brand {
  id: ID!
  name: String!
  website: String
  email: String!
  phone: String!
  logo: String
  slug: String
  welcomeMessage: String
  orderURL: String!
  paymentURL: String!
  totalRestaurants: Int!
  restaurants: [RestaurantBrief!]!
  owner: User!
}

type RestaurantBrief {
  id: ID!
  name: String!
  brandId: ID!
  address: String!
  point: Point
  phone: String
  email: String
}

input BrandInput {
  name: String!
  website: String
  email: String!
  phone: String!
  logo: String
  slug: String
  welcomeMessage: String
  orderURL: String!
  paymentURL: String!
  ownerId: ID!
}

type Query {
  brands(
    id: ID
    name: String
    phone: String
    ownerId: ID
  ): [Brand!]!
}

type Mutation {
  createBrand(input: BrandInput!): Brand!
  updateBrand(id: ID!, input: BrandInput): Brand!
  deleteBrand(id: ID!): Brand!
  addRestaurantToBrand(brandId: ID!, restaurantId: ID!): Brand!
  removeRestaurantFromBrand(brandId: ID!, restaurantId: ID!): Brand!
}
