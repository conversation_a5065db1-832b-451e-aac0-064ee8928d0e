# ============================================================================
# Customer Address Types
# ============================================================================

"""
Address type used for customer's saved addresses and postcode lookup
placeId and coordinates might not accurately represent the address because they are not visible to the customer
"""
type CustomerAddress {
  placeId: String! # Google Places 唯一标识符
  formattedAddress: String! # Google 标准化的完整地址
  streetAddress: String! # 街道地址（包含门牌号）
  city: String! # 城市
  state: String # 州/省
  postcode: String! # 邮编
  country: String! # 国家
  coordinates: Coordinates! # 地理坐标
  # Customer-specific fields (optional for postcode lookup)
  addressId: ID
  recipientName: String
  phone: String
  isDefault: Boolean
  tag: String
  createdAt: DateTime
  updatedAt: DateTime
}

"""
Address details returned from postcode lookup
""" #TODO: remove this
type AddressDetails {
  addressLine1: String
  addressLine2: String
  city: String
  state: String
  country: String
  postcode: String!
  coordinates: Coordinates!
}

# ============================================================================
# Core Customer Type
# ============================================================================

type Customer {
  customerId: ID!
  name: String
  email: String
  phone: String!
  isActive: Boolean!
  createdAt: String!
  updatedAt: String!
  addressCount: Int!
  addresses: [CustomerAddress!] #max 20 addresses
  totalOrders: Int!
  totalAmount: Float!
  orderHistory: [OrderBrief!]
  brandOrderCount: Int!
  hasMore: Boolean!
  customerTag: [String!]
}
input AddressInput {
  placeId: String!
  streetAddress: String!
  city: String!
  state: String
  postcode: String!
  country: String!
  coordinates: CoordinatesInput!
  recipientName: String!
  phone: String!
  tag: String
}
# ============================================================================
# Customer Operations
# ============================================================================

extend type Query {
  # modifyed by Lance
  customerbyPhone(phone: String!, restaurantBrandId: ID!, orderoffset: Int = 0, orderlimit: Int = 10): Customer
  getAddressFromPostcode(postcode: String!, country: String! = "IE"): CustomerAddress!
  customerAddresses(customerId: String!, offset: Int = 0, limit: Int = 10): [CustomerAddress!]!
}

extend type Mutation {
  addCustomerAddress(customerId: ID!, address: AddressInput!): CustomerAddress!
  deleteCustomerAddress(customerId: ID!, addressId: ID!): Boolean!
}
