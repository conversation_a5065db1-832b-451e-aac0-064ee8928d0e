## address management function

URL: same as checkout, but with address selection.

Style: the design should use the same style as the rest of the website

after checkout, just to page of address selection

### Page of selecting address

- get addresses from graphQL query (customerAddresses), need to pass customerId and token
- List of all addresses (maximum 20 items) with option to edit or delete
- button to add new address

### Page of adding new address or edit address

- Eircode/Postcode input: query address from graphQL query (getAddressFromPostcode), query button,
- result will be present below, editable
- textbox to input phone number
- confirm button
- if it is edit address, these fields will be pre-filled
after confirm, back to address selection page
