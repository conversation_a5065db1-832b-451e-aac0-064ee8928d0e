{"name": "restaurant-ordering-website", "version": "1.0.0", "description": "A React-based restaurant ordering website that interacts with a GraphQL server.", "main": "index.js", "scripts": {"start": "vite", "build": "vite build", "serve": "vite preview", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\""}, "dependencies": {"@ant-design/icons": "^5.2.6", "@apollo/client": "^3.12.4", "@types/node": "^20.11.24", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@vitejs/plugin-react": "^4.3.4", "antd": "^5.11.1", "graphql": "^16.8.1", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.22.3", "vite": "^4.5.5"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.8.7"}, "keywords": ["restaurant", "ordering", "graphql", "react"], "author": "", "license": "ISC", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}