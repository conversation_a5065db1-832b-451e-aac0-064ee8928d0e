[build]
  command = "npm run build"
  publish = "dist"

# Handle OPTIONS preflight requests for CORS
[[redirects]]
  from = "/api/whatsapp/*"
  to = "/.netlify/functions/cors-handler"
  status = 200
  force = true
  conditions = {Method = ["OPTIONS"]}

# Proxy for WhatsApp API to handle CORS
[[redirects]]
  from = "/api/whatsapp/*"
  to = "https://whatsapp.imyth.org/whatsapp/:splat"
  status = 200
  force = true
  headers = {
    Access-Control-Allow-Origin = "*",
    Access-Control-Allow-Methods = "GET, POST, OPTIONS",
    Access-Control-Allow-Headers = "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-WhatsAppW-Token, x-dialogue-id",
    X-From-Netlify = "true"
  }

# SPA fallback
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200