import { useState, useCallback } from 'react';
import { ApolloError } from '@apollo/client';

// WhatsApp error codes
enum WhatsAppErrorCode {
  INVALID_TOKEN = 'WHATSAPP_INVALID_TOKEN',
  EXPIRED_SESSION = 'WHATSAPP_EXPIRED_SESSION',
  MISSING_TOKEN = 'WHATSAPP_MISSING_TOKEN',
  INVALID_CUSTOMER = 'WHATSAPP_INVALID_CUSTOMER',
}

// Error messages for specific error codes
const ERROR_MESSAGES = {
  [WhatsAppErrorCode.INVALID_TOKEN]: 'Invalid token. Please send a message in WhatsApp to get a new link.',
  [WhatsAppErrorCode.EXPIRED_SESSION]: 'Your session has expired. Please send a message in WhatsApp to get a new link.',
  [WhatsAppErrorCode.MISSING_TOKEN]: 'Authentication required. Please send a message in WhatsApp to get a new link.',
  [WhatsAppErrorCode.INVALID_CUSTOMER]: 'Customer not found. Please send a message in WhatsApp to get a new link.',
};

/**
 * Custom hook for handling errors in a consistent way
 */
export function useErrorHandler() {
  const [error, setError] = useState<string | null>(null);
  
  /**
   * Handle any error and extract a user-friendly message
   */
  const handleError = useCallback((err: any): string => {
    // Default error message
    let errorMessage = 'An unexpected error occurred';
    
    if (err instanceof ApolloError) {
      // Handle GraphQL errors
      if (err.graphQLErrors && err.graphQLErrors.length > 0) {
        const graphQLError = err.graphQLErrors[0];
        
        // First check if there's a meaningful server message
        if (graphQLError.message && 
            !['Unauthorized', 'Forbidden'].includes(graphQLError.message)) {
          errorMessage = graphQLError.message;
        } 
        // Then check for specific error codes
        else if (graphQLError.extensions?.code) {
          const code = graphQLError.extensions.code as string;
          if (code in ERROR_MESSAGES) {
            errorMessage = ERROR_MESSAGES[code as WhatsAppErrorCode];
          }
        }
      } 
      // Handle network errors
      else if (err.networkError) {
        if ('statusCode' in err.networkError && err.networkError.statusCode === 401) {
          errorMessage = ERROR_MESSAGES[WhatsAppErrorCode.MISSING_TOKEN];
        } else {
          errorMessage = `Network error: ${err.message}`;
        }
      } 
      // Use general error message if available
      else if (err.message) {
        errorMessage = err.message;
      }
    } 
    // Handle non-Apollo errors
    else if (err instanceof Error) {
      errorMessage = err.message;
    } else if (typeof err === 'string') {
      errorMessage = err;
    }
    
    // Set the error state
    setError(errorMessage);
    return errorMessage;
  }, []);
  
  /**
   * Clear the current error
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  return { error, handleError, clearError };
}
