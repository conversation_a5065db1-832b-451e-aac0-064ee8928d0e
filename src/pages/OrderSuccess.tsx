import React, { useEffect, useState } from 'react';
import '../styles/OrderSuccess.css';

const OrderSuccess: React.FC = () => {
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    // 检查是否有打开者
    const hasOpener = window.opener && !window.opener.closed;
    if (!hasOpener) {
      console.warn('No opener window found, this page should be opened from WhatsApp');
    }

    // 每秒更新倒计时
    const countdownInterval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          try {
            // 先聚焦到打开者
            if (hasOpener) {
              window.opener.focus();
              // 给打开者一点时间来获得焦点
              setTimeout(() => {
                window.close();
              }, 100);
            } else {
              // 如果没有打开者，直接关闭
              window.close();
            }
          } catch (error) {
            console.error('Error while closing window:', error);
            window.close();
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(countdownInterval);
  }, []);

  return (
    <div className="order-success">
      <div className="success-content">
        <h1>Order placed successfully!</h1>
        <p>Thank you for your order.</p>
        <p>Please check WhatsApp for next step.</p>
        <p className="closing-note">This window will close in {countdown} seconds...</p>
      </div>
    </div>
  );
};

export default OrderSuccess;
