import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from 'antd';
import '../styles/Error.css';

interface ErrorState {
  error: string;
  title: string;
  returnPath?: string;
  returnText?: string;
}

const ErrorPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const state = location.state as ErrorState;

  // 如果没有错误状态，使用默认值
  const errorMessage = state?.error || 'An unexpected error occurred';
  const errorTitle = state?.title || 'Error';
  const returnPath = state?.returnPath || '/';
  const returnText = state?.returnText || 'Close Window';

  // 不再需要路径类型，因为我们现在使用统一的关闭窗口行为

  const handleReturn = () => {
    // 如果提供了返回路径，使用它
    if (returnPath && returnPath !== '/') {
      navigate(returnPath);
    } else {
      // 否则，关闭窗口
      window.close();

      // 如果 window.close() 不起作用（某些浏览器限制），则返回首页
      setTimeout(() => {
        navigate('/');
      }, 300);
    }
  };

  return (
    <div className="error-container">
      <div className="error-content">
        <h1>{errorTitle}</h1>
        <p className="error-message">{errorMessage}</p>
        <Button type="primary" onClick={handleReturn}>
          {returnText}
        </Button>
      </div>
    </div>
  );
};

export default ErrorPage;
