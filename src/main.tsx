function updateVh() {
  const vh = window.innerHeight * 0.01;
  document.documentElement.style.setProperty('--vh', `${vh}px`);
}

// Initialize on load
window.addEventListener('load', updateVh);
updateVh();

// Update on resize with debounce (150ms)
let resizeTimeout: NodeJS.Timeout;
window.addEventListener('resize', () => {
  clearTimeout(resizeTimeout);
  resizeTimeout = setTimeout(updateVh, 150);
});

// Update on orientation change with delay (300ms)
let orientationTimeout: NodeJS.Timeout;
window.addEventListener('orientationchange', () => {
  clearTimeout(orientationTimeout);
  orientationTimeout = setTimeout(updateVh, 300);
});
