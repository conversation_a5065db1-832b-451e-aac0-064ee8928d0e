interface AddressPayload {
  placeId: string;
  streetAddress: string;
  city: string;
  state: string;
  postcode: string;
  country: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  recipientName: string;
  phone: string;
  tag?: string;
}

import { extractTokenFromPath } from '../utils/tokenUtils';
import { handleWhatsAppHTTPError, WHATSAPP_ERROR_TYPES } from '../utils/errorHandling';

export async function submitAddress(payload: AddressPayload): Promise<any> {
  const token = extractTokenFromPath();
  const directUrl = `${import.meta.env.VITE_API_URL}/whatsapp/submit-addr`;

  const response = await fetch(directUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-WhatsAppW-Token': token, // Using X-WhatsAppW-Token header as per specification
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    // Get error text to parse
    const errorText = await response.text();
    console.error(`Server error (${response.status}):`, errorText);

    // Handle 401 errors specifically
    if (response.status === 401) {
      const handled = handleWhatsAppHTTPError(401, errorText);
      if (handled) {
        throw new Error(WHATSAPP_ERROR_TYPES.MISSING_TOKEN.message);
      }
    }

    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
}
