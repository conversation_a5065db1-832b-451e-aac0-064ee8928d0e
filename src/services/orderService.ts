import { extractTokenFromPath } from '../utils/tokenUtils';
import { handleWhatsAppHTTPError, WHATSAPP_ERROR_TYPES } from '../utils/errorHandling';

interface AddonsInput {
  _id: string;
  options: string[];
}

interface OrderInput {
  food: string;
  quantity: number;
  variation: string;
  addons: AddonsInput[];
  specialInstructions?: string;
}

interface OrderPayload {
  orderInput: OrderInput[];
}

interface OrderWithAddressPayload extends OrderPayload {
  deliveryAddressId?: string; // 可选，如果是自取订单则不需要
  restaurantId: string;
  instructions?: string;
  tipping?: number;
  isPickedUp: boolean; // 标记是否为自取订单
}

export interface OrderResponse {
  success: boolean;
  address_selected?: boolean;
  order_id?: string;
  [key: string]: any; // 允许其他可能的字段
}

export async function submitOrder(payload: OrderPayload): Promise<OrderResponse> {
  // Use the common token extraction utility
  const token = extractTokenFromPath();

  // Use direct API call
  const directUrl = `${import.meta.env.VITE_API_URL}/whatsapp/submit-cart`;

  // Log the payload for debugging
  console.log('Submitting order with payload:', JSON.stringify(payload, null, 2));

  // Ensure all required fields are present and correctly formatted
  const validatedPayload = {
    ...payload,
    orderInput: payload.orderInput.map(item => ({
      ...item,
      food: String(item.food),
      quantity: Number(item.quantity),
      variation: item.variation || '',
      specialInstructions: item.specialInstructions || '',
    })),
  };

  const response = await fetch(directUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-WhatsAppW-Token': token,
    },
    body: JSON.stringify(validatedPayload),
  });

  // Log the response status and headers for debugging
  console.log('Response status:', response.status);
  console.log('Response headers:', [...response.headers.entries()]);

  if (!response.ok) {
    const errorText = await response.text();
    console.error('Server error response:', errorText);

    // Handle 401 Unauthorized errors first
    if (response.status === 401) {
      // Try to handle with our specialized function
      const handled = handleWhatsAppHTTPError(401, errorText);
      if (handled) {
        throw new Error(WHATSAPP_ERROR_TYPES.MISSING_TOKEN.message);
      }
    }

    try {
      // Try to parse as JSON if possible
      const errorJson = JSON.parse(errorText);

      // Handle specific error cases
      if (response.status === 404) {
        throw new Error('Menu not found. The URL might have expired.');
      }

      // If the server sends a specific message about URL expiration
      if (errorJson.code === 'URL_EXPIRED' || errorJson.message?.toLowerCase().includes('expired')) {
        throw new Error('This menu link has expired. Please request a new one.');
      }

      // Check for authorization errors in the error message
      if (errorJson.error === 'Missing or invalid authorization header') {
        throw new Error(WHATSAPP_ERROR_TYPES.MISSING_TOKEN.message);
      }

      throw new Error(errorJson.message || `Server error (${response.status}): ${response.statusText}`);
    } catch (jsonError) {
      // If not valid JSON or other error
      if (response.status === 404) {
        throw new Error('Menu not found. The URL might have expired.');
      }
      throw new Error(`Server error (${response.status}): ${response.statusText}\n${errorText.substring(0, 200)}...`);
    }
  }

  try {
    const responseText = await response.text();
    console.log('Server response text:', responseText);

    // Only try to parse as JSON if there's actual content
    if (responseText.trim()) {
      const parsedResponse = JSON.parse(responseText) as OrderResponse;
      return parsedResponse;
    } else {
      // Default success response if empty
      return { success: true, address_selected: false };
    }
  } catch (e) {
    console.error('Error parsing response:', e);
    throw new Error(`Invalid response from server: ${e instanceof Error ? e.message : String(e)}`);
  }
}

// 添加新的提交订单函数，包含地址信息
export async function submitOrderWithAddress(payload: OrderWithAddressPayload): Promise<OrderResponse> {
  // 使用通用的令牌提取工具
  const token = extractTokenFromPath();

  // 使用直接API调用
  const directUrl = `${import.meta.env.VITE_API_URL}/whatsapp/submit-cart`;

  // 记录有效载荷以进行调试
  console.log('Submitting order with address:', JSON.stringify(payload, null, 2));

  // 确保所有必填字段都存在且格式正确
  let validatedPayload: any = {
    ...payload,
    orderInput: payload.orderInput.map(item => ({
      ...item,
      food: String(item.food),
      quantity: Number(item.quantity),
      variation: item.variation || '',
      specialInstructions: item.specialInstructions || '',
    })),
    restaurantId: String(payload.restaurantId),
    instructions: payload.instructions || '',
    tipping: Number(payload.tipping || 0),
  };

  // 添加isPickedUp字段（现在是必需的）
  validatedPayload.isPickedUp = payload.isPickedUp;

  // 如果不是自取订单，必须有地址ID
  if (!payload.isPickedUp) {
    if (!payload.deliveryAddressId) {
      throw new Error('Delivery address is required for non-pickup orders');
    }
    validatedPayload.deliveryAddressId = String(payload.deliveryAddressId);
  }

  const response = await fetch(directUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-WhatsAppW-Token': token,
    },
    body: JSON.stringify(validatedPayload),
  });

  // 记录响应状态和标头以进行调试
  console.log('Response status:', response.status);
  console.log('Response headers:', [...response.headers.entries()]);

  if (!response.ok) {
    // 错误处理逻辑与submitOrder相同
    const errorText = await response.text();
    console.error('Server error response:', errorText);

    if (response.status === 401) {
      const handled = handleWhatsAppHTTPError(401, errorText);
      if (handled) {
        throw new Error(WHATSAPP_ERROR_TYPES.MISSING_TOKEN.message);
      }
    }

    try {
      const errorJson = JSON.parse(errorText);

      if (response.status === 404) {
        throw new Error('Menu not found. The URL might have expired.');
      }

      if (errorJson.code === 'URL_EXPIRED' || errorJson.message?.toLowerCase().includes('expired')) {
        throw new Error('This menu link has expired. Please request a new one.');
      }

      if (errorJson.error === 'Missing or invalid authorization header') {
        throw new Error(WHATSAPP_ERROR_TYPES.MISSING_TOKEN.message);
      }

      throw new Error(errorJson.message || `Server error (${response.status}): ${response.statusText}`);
    } catch (jsonError) {
      if (response.status === 404) {
        throw new Error('Menu not found. The URL might have expired.');
      }
      throw new Error(`Server error (${response.status}): ${response.statusText}\n${errorText.substring(0, 200)}...`);
    }
  }

  try {
    const responseText = await response.text();
    console.log('Server response text:', responseText);

    if (responseText.trim()) {
      const parsedResponse = JSON.parse(responseText) as OrderResponse;
      return parsedResponse;
    } else {
      return { success: true };
    }
  } catch (e) {
    console.error('Error parsing response:', e);
    throw new Error(`Invalid response from server: ${e instanceof Error ? e.message : String(e)}`);
  }
}
