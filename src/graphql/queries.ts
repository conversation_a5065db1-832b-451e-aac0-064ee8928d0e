import { gql } from '@apollo/client';

// Get session data for pre-filling cart
export const GET_SESSION_DATA = gql`
  query GetSessionData($token: String!) {
    getSessionByToken(token: $token) {
      restaurantId
      customerId
      orderInput {
        food
        variation
        quantity
        addons {
          _id
          options
        }
        specialInstructions
      }
      paymentMethod
      couponCode
      deliveryAddressId
      tipping
      orderDate
      isPickedUp
      taxationAmount
      deliveryCharges
      instructions
      itemsSubtotal
      orderAmount
    }
  }
`;

// Get customer addresses
export const GET_CUSTOMER_ADDRESSES = gql`
  query GetAddresses($offset: Int, $limit: Int) {
    customerAddresses(offset: $offset, limit: $limit) {
      addressId
      formattedAddress
      streetAddress
      city
      state
      postcode
      country
      coordinates {
        longitude
        latitude
      }
      recipientName
      phone
      tag
      isDefault
    }
  }
`;

// Get address from postcode
export const GET_ADDRESS_FROM_POSTCODE = gql`
  query GetAddressFromPostcode($postcode: String!, $country: String) {
    getAddressFromPostcode(postcode: $postcode, country: $country) {
      placeId
      formattedAddress
      streetAddress
      city
      state
      postcode
      country
      coordinates {
        longitude
        latitude
      }
    }
  }
`;

// Add customer address
export const ADD_CUSTOMER_ADDRESS = gql`
  mutation AddAddress($address: AddressInput!) {
    addCustomerAddress(address: $address) {
      addressId
      formattedAddress
      streetAddress
      city
      postcode
      coordinates {
        longitude
        latitude
      }
    }
  }
`;

// Delete customer address
export const DELETE_CUSTOMER_ADDRESS = gql`
  mutation DeleteAddress($addressId: ID!) {
    deleteCustomerAddress(addressId: $addressId)
  }
`;
