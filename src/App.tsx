import React from 'react';
import './App.css';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Menu from './components/Menu';
import Cart from './components/Cart';
import AddressSelection from './pages/AddressSelection';
import AddressForm from './pages/AddressForm';
import OrderSuccess from './pages/OrderSuccess';
import ErrorPage from './pages/ErrorPage';
import { CartProvider } from './context/CartContext';

const App = () => {
  return (
    <CartProvider>
      <Router>
        <div className="App">
          <Routes>
            {/* Menu routes */}
            <Route
              path="/m"
              element={
                <>
                  <Menu />
                  <Cart />
                </>
              }
            />
            <Route
              path="/m/:token"
              element={
                <>
                  <Menu />
                  <Cart />
                </>
              }
            />

            {/* Error page */}
            <Route path="/error" element={<ErrorPage />} />

            {/* Address routes */}
            <Route path="/a" element={<AddressSelection />} />
            <Route path="/a/:token" element={<AddressSelection />} />
            <Route path="/a/:token/add" element={<AddressForm />} />
            <Route path="/a/:token/edit/:addressId" element={<AddressForm />} />

            {/* Legacy routes - 保留向后兼容性 */}
            <Route path="/address" element={<AddressSelection />} />
            <Route path="/address/add" element={<AddressForm />} />
            <Route path="/address/edit/:addressId" element={<AddressForm />} />

            {/* Success page */}
            <Route path="/success" element={<OrderSuccess />} />
          </Routes>
        </div>
      </Router>
    </CartProvider>
  );
};

export default App;
