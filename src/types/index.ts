export interface Menu {
  Description: string | null;
  MenuId: number;
  MenuVersionNumber: number;
  VersionGuid: string;
  MenuSections: MenuSection[];
  GlobalOptionSets: MenuItemOptionSet[];
  GlobalOptionItems: MenuItemOptionSetItem[];
  MenuSectionBehaviour: number;
  DisplaySectionLinks: boolean;
  ConcessionStores: any[];
  CatalogItems: any[];
}

export interface MenuSection {
  MenuSectionId: number;
  Name: string;
  Description: string | null;
  DisplayOrder: number;
  MenuItems: MenuItem[];
  PublicId: string;
  IsDeleted: boolean;
  IsAvailable: boolean;
  IsHiddenFromUsers: boolean;
  ImageName: string | null;
  ImageUrl: string | null;
  CellAspectRatio: number;
  CellLayoutType: number;
  MenuSectionAvailability: MenuSectionAvailability | null;
  ConcessionStoreId: string | null;
  MenuSectionMetadata: any[];
  ExternalImageUrl: string | null;
}

export interface MenuItem {
  MenuId: number;
  MenuItemId: number;
  Name: string;
  Description: string;
  SpicinessRating: number | null;
  Price: number;
  DepositReturnFee: number | null;
  DisplayOrder: number;
  IsDeleted: boolean;
  Alcohol: boolean;
  CatalogItemId: string | null;
  Tags: string[];
  PublicId: string;
  IsAvailable: boolean;
  MenuItemOptionSets: MenuItemOptionSetRef[];
  TaxRate: number | null;
  TaxRateId: string | null;
  TaxValue: number;
  TaxRateName: string | null;
  MenuSectionId: number;
  ImageName: string | null;
  ImageUrl: string | null;
  CellAspectRatio: number;
  CellLayoutType: number;
  ActualPrice: number;
  DisableVouchers: boolean;
  ExcludeFromVoucherDiscounting: boolean;
  DailySpecialHours: DailySpecialHours[];
  PriceCanIncrease: boolean;
  MenuItemMetadata: any[];
  ExternalImageUrl: string | null;
}

export interface MenuItemOptionSetRef {
  MenuItemOptionSetId: string;
  DisplayOrder: number;
}

export interface MenuItemOptionSet {
  MenuItemOptionSetId: string;
  CatalogItemId: string | null;
  Name: string;
  IsMasterOptionSet: boolean;
  MinSelectCount: number;
  MaxSelectCount: number;
  IsDeleted: boolean;
  MenuItemOptionSetItems: MenuItemOptionSetItemRef[];
  ImageName: string | null;
  ImageUrl: string | null;
  CellAspectRatio: number;
  CellLayoutType: number;
  MinPrice: number;
  MenuItemOptionSetMetadata: any[];
  ExternalImageUrl: string | null;
}

export interface MenuItemOptionSetItemRef {
  MenuItemOptionSetItemId: string;
  DisplayOrder: number;
}

export interface MenuItemOptionSetItem {
  MenuItemOptionSetItemId: string;
  CatalogItemId: string | null;
  Name: string;
  Price: number;
  DepositReturnFee: number | null;
  TaxRateId: string | null;
  TaxRate: number | null;
  TaxValue: number;
  TaxRateName: string | null;
  IsAvailable: boolean;
  IsDeleted: boolean;
  Tags: string[];
  ImageName: string | null;
  ImageUrl: string | null;
  CellAspectRatio: number;
  CellLayoutType: number;
  OptionSetItemMetadata: any[];
  ExternalImageUrl: string | null;
}

export interface MenuSectionAvailability {
  MenuSectionId: number;
  AvailableTimes: BusinessHoursPeriod[] | null;
  AvailabilityMode: number;
}

export interface BusinessHoursPeriod {
  BusinessHoursPeriodId: number;
  DayOfWeek: number;
  StartTime: string;
  Period: string;
  StartTimeEarly: string | null;
  PeriodEarly: string | null;
}

export interface DailySpecialHours {
  BusinessHoursPeriodId: number;
  DayOfWeek: number;
  StartTime: string;
  Period: string;
  StartTimeEarly: string | null;
  PeriodEarly: string | null;
}

export interface Query {
  menu: Menu;
}

export interface CartItem {
  menuItemId: string;
  name: string;
  price: number;
  quantity: number;
  selectedOptions: {
    optionSetId: string;
    optionSetName: string;
    options: {
      id: string;
      name: string;
      price: number;
    }[];
  }[];
}

export interface CartState {
  items: CartItem[];
  total: number;
}

export interface MenuItemType {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
}
