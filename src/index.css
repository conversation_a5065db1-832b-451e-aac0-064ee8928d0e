:root {
  /* Primary Colors */
  --primary-color: #c41e3a;
  --primary-color-dark: #a01830;
  --primary-color-light: #e65100;

  /* Secondary Colors */
  --secondary-color: #4caf50;
  --secondary-color-dark: #45a049;
  --secondary-color-light: #40a9ff;

  /* Neutral Colors */
  --text-primary: #333;
  --text-secondary: #666;
  --border-color: #ddd;
  --background-light: #f8f8f8;
  --background-lighter: #f5f5f5;
  --background-white: #fff;
  --border-light: #e8e8e8;
  --border-lighter: #eee;

  /* Overlay Colors */
  --overlay-background: rgba(0, 0, 0, 0.5);
  --shadow-color: rgba(0, 0, 0, 0.1);
  --shadow-color-dark: rgba(0, 0, 0, 0.2);
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', '<PERSON>ra Sans',
    'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}
