/* Cart Fixed at Bottom */
.cart-container {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  z-index: 1000;
  background-color: var(--primary-color);
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 15rem;
  max-width: 25rem;
}

/* Cart <PERSON>er (Always visible) */
.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  color: white;
}

.cart-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.cart-badge {
  position: relative;
  background-color: white;
  color: var(--primary-color);
  border-radius: 50%;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: bold;
  margin-right: 0.5rem;
}

.cart-total {
  font-weight: bold;
  font-size: 1.25rem;
}

/* Cart Dropdown (Expandable Content) */
.cart-dropdown {
  display: block;
  background: white;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 0.5rem;
  max-height: calc(var(--vh, 1vh) * 70);
  overflow-y: auto;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.cart-container.expanded {
  cursor: default;
}

/* Cart Items */
.cart-items {
  margin: 0.5rem 0;
  max-height: calc(var(--vh, 1vh) * 50);
  overflow-y: auto;
}

.cart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #eee;
}

.cart-item-details {
  flex: 1;
}

.cart-item-details h3 {
  margin: 0;
  font-size: 1rem;
  color: #333;
}

.item-options {
  margin: 0.25rem 0 0;
  font-size: 0.875rem;
  color: #666;
}

.cart-item-price {
  font-weight: bold;
  margin: 0 1rem;
  color: #333;
}

.cart-item-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quantity-btn {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  border: 1px solid #ddd;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1rem;
  padding: 0;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.item-quantity {
  margin: 0 0.25rem;
  font-weight: bold;
}

.remove-btn {
  background: none;
  border: none;
  color: #ff5252;
  cursor: pointer;
  font-size: 1.25rem;
  padding: 0;
  margin-left: 0.5rem;
}

/* Empty Cart */
.empty-cart {
  text-align: center;
  padding: 2rem 0;
  color: #666;
}

/* Cart Summary */
.cart-summary {
  margin-top: 1rem;
  border-top: 1px solid #eee;
  padding-top: 1rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #666;
}

.summary-row.total {
  font-weight: bold;
  font-size: 1rem;
  color: #333;
  margin-top: 0.5rem;
  border-top: 1px solid #eee;
  padding-top: 0.5rem;
}

/* Buttons */
.checkout-btn {
  width: 100%;
  padding: 0.75rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  margin-top: 1rem;
}

.checkout-btn:hover {
  background: var(--primary-color-dark, #0056b3);
}

.checkout-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.cancel-btn {
  width: 100%;
  padding: 0.75rem;
  background: #f8f9fa;
  color: #dc3545;
  border: 1px solid #dc3545;
  border-radius: 0.25rem;
  font-size: 1rem;
  cursor: pointer;
  margin-top: 0.5rem;
}

.cancel-btn:hover {
  background: #dc3545;
  color: white;
}

.cancel-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Success and Error Messages */
.checkout-success {
  background-color: #d4edda;
  color: #155724;
  padding: 0.75rem;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
}

.checkout-error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
}

.order-details {
  padding: 0.5rem;
  border-top: 1px solid var(--border-light);
  margin-top: 0.5rem;
}

.order-details-row {
  margin-bottom: 0.5rem;
}

.order-details-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  display: block;
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.tipping-input-container {
  display: flex;
  align-items: center;
  border: 1px solid var(--border-light);
  border-radius: 4px;
  overflow: hidden;
  height: 2rem;
}

.currency-symbol {
  padding: 0 0.5rem;
  background-color: #f5f5f5;
  color: var(--text-secondary);
  border-right: 1px solid var(--border-light);
  height: 100%;
  display: flex;
  align-items: center;
}

.tipping-input {
  flex: 1;
  padding: 0 0.5rem;
  border: none;
  outline: none;
  width: 100%;
  height: 100%;
}

.instructions-input {
  width: 100%;
  padding: 0.25rem 0.5rem;
  border: 1px solid var(--border-light);
  border-radius: 4px;
  resize: none;
  font-size: 0.9rem;
  line-height: 1.2;
}

.tipping-input:focus,
.instructions-input:focus {
  border-color: var(--primary-color);
}
