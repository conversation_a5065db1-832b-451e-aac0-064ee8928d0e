/* Error Message Styles */
.checkout-error {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
  padding: 12px;
  margin: 10px 0;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkout-error::before {
  content: '⚠️';
  font-size: 16px;
}

/* Error Page Styles */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(var(--vh, 1vh) * 100);
  background-color: #f5f5f5;
  padding: 20px;
}

.error-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 40px;
  max-width: 500px;
  width: 100%;
  text-align: center;
}

.error-content h1 {
  color: #ff4d4f;
  margin-bottom: 20px;
  font-size: 24px;
}

.error-message {
  margin-bottom: 30px;
  color: #333;
  font-size: 16px;
  line-height: 1.6;
}
