/* Address Management Styles */
.address-container {
  width: 95%;
  margin: 0 auto;
  padding: 1rem;
  box-sizing: border-box;
  /* 使用 clamp 函数设置响应式最大宽度 */
  /* 最小值, 首选值, 最大值 */
  max-width: clamp(320px, 90%, 900px);
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.address-header h1 {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin: 0;
}

.address-list {
  background-color: var(--background-white);
  border-radius: 0.5rem;
  box-shadow: 0 0.25rem 0.5rem var(--shadow-color);
  overflow: hidden;
}

.address-item {
  border-bottom: 1px solid var(--border-light);
  padding: 1rem;
  transition: background-color 0.3s;
}

.address-item:last-child {
  border-bottom: none;
}

.address-item:hover {
  background-color: var(--background-lighter);
}

.address-item-title {
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.address-item-content {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  word-break: break-word;
  text-align: left;
}

.address-item-content p {
  margin: 0;
  line-height: 1.3;
  padding: 2px 0;
}

.address-item-actions {
  display: flex;
  gap: 0.5rem;
}

.address-form {
  background-color: var(--background-white);
  border-radius: 0.5rem;
  box-shadow: 0 0.25rem 0.5rem var(--shadow-color);
  padding: 1.5rem;
}

.address-form-title {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.primary-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.primary-button:hover {
  background-color: var(--primary-color-dark);
}

.secondary-button {
  background-color: white;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.3s, border-color 0.3s;
}

.secondary-button:hover {
  background-color: var(--background-lighter);
  border-color: var(--text-secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .address-container {
    width: 100%;
    padding: 0.5rem;
  }

  .address-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .address-item-actions {
    flex-direction: column;
  }

  /* 移动端按钮组样式 */
  .address-item .ant-list-item-meta {
    margin-bottom: 0;
  }
}

/* 小屏幕设备按钮样式调整 */
@media (max-width: 576px) {
  /* 针对 AddressSelection 中的按钮组 */
  .address-item [style*='display: flex'][style*='gap'] {
    flex-wrap: wrap;
  }

  .address-item [style*='display: flex'][style*='gap'] button {
    font-size: 12px;
    padding: 0 8px;
  }
}

/* 平板设备 */
@media (min-width: 769px) and (max-width: 1024px) {
  .address-container {
    max-width: clamp(600px, 85%, 800px);
  }
}

/* 大屏设备 */
@media (min-width: 1025px) {
  .address-container {
    max-width: clamp(800px, 75%, 900px);
  }
}

/* 横屏模式 */
@media (orientation: landscape) and (max-height: 500px) {
  .address-container {
    max-width: 90%;
  }

  .address-form {
    padding: 1rem;
  }
}

.address-footer {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}

.no-order-warning {
  background-color: #fff1f0;
  border: 1px solid #ffa39e;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  text-align: center;
}

.no-order-warning p {
  margin-bottom: 10px;
}

/* 空地址列表样式 */
.empty-address-container {
  text-align: center;
  padding: 20px;
}

.empty-address-container p {
  margin-bottom: 10px;
  color: #666;
}

/* 自取选项样式 */
.pickup-option {
  margin-bottom: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.3s;
}

.pickup-option:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #d9d9d9;
}

.pickup-header {
  text-align: center;
  margin-bottom: 16px;
}

.pickup-header h2 {
  font-size: 18px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pickup-header h2 .anticon {
  margin-right: 8px;
  font-size: 20px;
}

.pickup-header p {
  color: #666;
  margin: 0;
}

.divider-text {
  font-size: 14px;
  color: #999;
  padding: 0 8px;
}

/* Ant Design overrides */
.ant-list-item-meta-title {
  color: var(--text-primary) !important;
  text-align: left !important;
}

.ant-list-item-meta-description {
  color: var(--text-secondary) !important;
  text-align: left !important;
}

.ant-list-item-meta {
  align-items: flex-start !important;
  margin-bottom: 4px !important;
}

.ant-list-item-meta-content {
  padding: 0 15px !important;
  margin: 0 auto !important;
  max-width: 90% !important;
}

.ant-btn-primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.ant-btn-primary:hover {
  background-color: var(--primary-color-dark) !important;
  border-color: var(--primary-color-dark) !important;
}

.ant-btn-default {
  border-color: var(--border-color) !important;
}

.ant-form-item-label > label {
  color: var(--text-primary) !important;
}

.ant-input {
  border-color: var(--border-color) !important;
}

.ant-input:focus,
.ant-input-focused {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(196, 30, 58, 0.2) !important;
}
