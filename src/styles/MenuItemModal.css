.menu-item-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 2rem;
  z-index: 1000;
}

.menu-item-modal {
  background: white;
  padding: 1.25rem;
  border-radius: 0.5rem;
  max-width: 31.25rem;
  width: 90%;
  max-height: calc(var(--vh, 1vh) * 80);
  margin-bottom: calc(var(--vh, 1vh) * 8 + 1rem); /* 统一使用视口高度的8%加上1rem的基础边距 */
  overflow-y: auto;
  text-align: left;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  color: #666;
}

.modal-content {
  margin-bottom: 20px;
}

.item-description {
  color: #666;
  margin-bottom: 20px;
}

.option-set {
  margin: 20px 0;
}

.option-set h3 {
  margin-bottom: 10px;
  text-align: left;
  font-size: 1.2rem;
}

.option-set-hint {
  font-size: 0.9em;
  color: #666;
  text-align: left;
  margin-bottom: 10px;
}

.option {
  margin: 10px 0;
  display: flex;
  align-items: center;
}

.option input[type='radio'],
.option input[type='checkbox'] {
  margin: 0;
}

.option label {
  margin-left: 8px;
  cursor: pointer;
}

.variations-container {
  margin-bottom: 20px;
}

.variations-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.quantity-selector {
  margin: 20px 0;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.quantity-controls button {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 1px solid #ddd;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.quantity-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.order-summary {
  margin: 20px 0;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.summary-total {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.modal-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 16px;
  border-top: 1px solid #eee;
}

.modal-cancel-btn,
.modal-confirm-btn {
  min-width: 120px;
  height: 40px;
  padding: 0 20px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-cancel-btn {
  background-color: #f1f1f1;
  color: #333;
  flex: 1;
}

.modal-confirm-btn {
  background-color: var(--primary-color);
  color: white;
  flex: 2;
}

.modal-confirm-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
