.order-success {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(var(--vh, 1vh) * 100);
  background-color: #f8f9fa;
}

.success-content {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: 90%;
  width: clamp(280px, 90%, 400px);
}

.success-content h1 {
  color: #28a745;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.success-content p {
  color: #495057;
  margin: 0.5rem 0;
  font-size: 1.1rem;
}

.closing-note {
  margin-top: 2rem;
  color: #6c757d;
  font-size: 0.9rem;
}
