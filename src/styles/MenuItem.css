.menu-item {
  position: relative;
  padding: 16px;
  border: 1px solid var(--border-light);
  border-radius: 8px;
  background: var(--background-white);
}

.menu-item-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 12px;
}

.menu-item h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}

.menu-item p {
  margin: 0 0 8px 0;
  color: var(--text-secondary);
}

.menu-item .price {
  font-weight: bold;
  color: var(--primary-color);
}

.add-to-cart-btn {
  position: absolute;
  bottom: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-color);
  color: var(--background-white);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.add-to-cart-btn:hover {
  background: var(--primary-color-dark);
}
