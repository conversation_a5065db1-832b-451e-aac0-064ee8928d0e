import React, { useState, useEffect } from 'react';
import '../styles/MenuItemModal.css';
import { formatPrice } from '../config';

interface IOption {
  _id: string;
  title: string;
  description: string | null;
  price: number;
}

interface IAddon {
  _id: string;
  options: string[];
  title: string;
  description: string | null;
  quantityMinimum: number;
  quantityMaximum: number;
}

interface IVariation {
  _id: string;
  title: string;
  price: number;
  discounted: number | null;
  addons: string[];
}

interface IFood {
  _id: string;
  title: string;
  description: string;
  variations: IVariation[];
  image: string | null;
}

interface SelectedOption {
  optionSetId: string;
  optionIds: string[];
}

interface MenuItemModalProps {
  item: IFood;
  onClose: () => void;
  onConfirm: (item: IFood, variation: IVariation, quantity: number, selectedOptions: SelectedOption[]) => void;
  option_map: Record<string, IOption>;
  addon_map: Record<string, IAddon>;
  findOption: (optionId: string, variation: IVariation) => IOption | undefined;
}

const MenuItemModal: React.FC<MenuItemModalProps> = ({ item, onClose, onConfirm, option_map, addon_map, findOption }) => {
  const [selectedOptions, setSelectedOptions] = useState<Map<string, string[]>>(new Map());
  const [selectedVariationIndex, setSelectedVariationIndex] = useState<number>(0);
  const [quantity, setQuantity] = useState<number>(1);
  const selectedVariation = item.variations[selectedVariationIndex] || null;

  useEffect(() => {
    if (!selectedVariation || !selectedVariation.addons || !addon_map) {
      return;
    }

    // Initialize selected options based on addons
    const initialSelection = new Map<string, string[]>();
    selectedVariation.addons.forEach(addonId => {
      const addon = addon_map[addonId];
      if (addon) {
        // For radio buttons (min=1, max=1), preselect the first option
        const preselectedOptionIds =
          addon.quantityMinimum === 1 && addon.quantityMaximum === 1 && addon.options.length > 0 ? [addon.options[0]] : [];

        initialSelection.set(addonId, preselectedOptionIds);
      }
    });
    setSelectedOptions(initialSelection);
  }, [selectedVariation, addon_map]);

  const calculateTotalPrice = () => {
    if (!selectedVariation) return 0;

    const basePrice = selectedVariation.discounted || selectedVariation.price;
    let addonsPrice = 0;

    selectedOptions.forEach((optionIds, optionSetId) => {
      optionIds.forEach(optionId => {
        const option = option_map[optionId];
        if (option) {
          addonsPrice += option.price;
        }
      });
    });

    return (basePrice + addonsPrice) * quantity;
  };

  const handleVariationChange = (index: number) => {
    setSelectedVariationIndex(index);
  };

  const handleOptionChange = (optionSetId: string, optionId: string, isChecked: boolean) => {
    setSelectedOptions(prevOptions => {
      const newOptions = new Map(prevOptions);
      const currentSelection = newOptions.get(optionSetId) || [];
      const addon = addon_map[optionSetId];

      if (!addon) return newOptions;

      let updatedSelection: string[];

      // Handle radio button behavior (min=1, max=1)
      if (addon.quantityMinimum === 1 && addon.quantityMaximum === 1) {
        updatedSelection = isChecked ? [optionId] : [];
      }
      // Handle checkbox behavior
      else {
        if (isChecked) {
          // Add option if not already selected
          updatedSelection = [...currentSelection, optionId];

          // If we exceed max, remove the first option
          if (addon.quantityMaximum > 0 && updatedSelection.length > addon.quantityMaximum) {
            updatedSelection.shift();
          }
        } else {
          // Remove option
          updatedSelection = currentSelection.filter(id => id !== optionId);
        }
      }

      newOptions.set(optionSetId, updatedSelection);
      return newOptions;
    });
  };

  const incrementQuantity = () => {
    setQuantity(prev => prev + 1);
  };

  const decrementQuantity = () => {
    if (quantity > 1) {
      setQuantity(prev => prev - 1);
    }
  };

  const handleConfirm = () => {
    if (!selectedVariation) {
      console.error('No variation selected');
      return;
    }

    // Convert Map to array format
    const optionsArray: SelectedOption[] = [];
    selectedOptions.forEach((optionIds, optionSetId) => {
      optionsArray.push({
        optionSetId,
        optionIds,
      });
    });

    // Pass complete information including the food item and selected variation
    onConfirm(item, selectedVariation, quantity, optionsArray);
    onClose();
  };

  const isValid = () => {
    if (!selectedVariation) return false;

    // Check if all required addons have selections
    let valid = true;
    selectedVariation.addons.forEach(addonId => {
      const addon = addon_map[addonId];
      if (addon) {
        const selectedCount = selectedOptions.get(addonId)?.length || 0;
        if (selectedCount < addon.quantityMinimum) {
          valid = false;
        }
      }
    });

    return valid;
  };

  if (!item) {
    console.error('No item provided to modal');
    return null;
  }

  return (
    <div className="menu-item-modal-overlay" onClick={onClose}>
      <div className="menu-item-modal" onClick={e => e.stopPropagation()}>
        <div className="modal-header">
          <h3>{item.title}</h3>
          <button className="close-button" onClick={onClose}>
            ×
          </button>
        </div>

        <div className="modal-content">
          <p className="item-description">{item.description}</p>

          {/* Variations Selection */}
          {item.variations.length > 1 && (
            <div className="variations-container">
              <h3>Choose Size</h3>
              <div className="variations-list">
                {item.variations.map((variation, index) => (
                  <div key={variation._id} className="option">
                    <input
                      type="radio"
                      id={`variation-${variation._id}`}
                      name="variation"
                      checked={selectedVariationIndex === index}
                      onChange={() => handleVariationChange(index)}
                    />
                    <label htmlFor={`variation-${variation._id}`}>
                      {variation.title} - {formatPrice(variation.price)}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Addons Selection */}
          {selectedVariation && selectedVariation.addons && selectedVariation.addons.length > 0 && addon_map && option_map && (
            <div className="options-container">
              {selectedVariation.addons.map(addonId => {
                const addon = addon_map[addonId];
                if (!addon || !addon.options) return null;

                const validOptions = addon.options.filter(optionId => option_map[optionId]);
                if (validOptions.length === 0) return null;

                return (
                  <div key={addonId} className="option-set">
                    <h3>{addon.title}</h3>
                    {(addon.quantityMinimum !== 1 || addon.quantityMaximum !== 1) && (
                      <p className="option-set-hint">
                        {addon.quantityMinimum === addon.quantityMaximum
                          ? `Select ${addon.quantityMinimum} options`
                          : `Select ${addon.quantityMinimum} to ${addon.quantityMaximum} options`}
                      </p>
                    )}
                    {validOptions.map(optionId => {
                      const option = option_map[optionId];
                      if (!option) return null;

                      const isSelected = selectedOptions.get(addonId)?.includes(optionId) || false;

                      return (
                        <div key={optionId} className="option">
                          <input
                            type={addon.quantityMaximum === 1 ? 'radio' : 'checkbox'}
                            id={`option-${optionId}`}
                            name={`optionSet-${addonId}`}
                            checked={isSelected}
                            onChange={e => handleOptionChange(addonId, optionId, e.target.checked)}
                          />
                          <label htmlFor={`option-${optionId}`}>
                            {option.title}
                            {option.price > 0 && ` (+${formatPrice(option.price)})`}
                          </label>
                        </div>
                      );
                    })}
                  </div>
                );
              })}
            </div>
          )}

          {/* Quantity Selector */}
          <div className="quantity-selector">
            <h3>Quantity</h3>
            <div className="quantity-controls">
              <button onClick={decrementQuantity} disabled={quantity <= 1}>
                -
              </button>
              <span>{quantity}</span>
              <button onClick={incrementQuantity}>+</button>
            </div>
          </div>

          {/* Order Summary */}
          <div className="order-summary">
            <h3>Order Summary</h3>
            <div className="summary-total">
              <span>Total:</span>
              <span>{formatPrice(calculateTotalPrice())}</span>
            </div>
          </div>
        </div>

        <div className="modal-actions">
          <button className="modal-cancel-btn" onClick={onClose}>
            Cancel
          </button>
          <button className="modal-confirm-btn" onClick={handleConfirm} disabled={!isValid()}>
            Add to Cart
          </button>
        </div>
      </div>
    </div>
  );
};

export default MenuItemModal;
