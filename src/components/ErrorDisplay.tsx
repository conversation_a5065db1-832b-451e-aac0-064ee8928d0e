import React from 'react';
import { Button } from 'antd';

interface ErrorDisplayProps {
  error: string | null;
  onAction?: () => void;
  actionText?: string;
  title?: string;
}

/**
 * Component for displaying error messages with an optional action button
 */
const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ 
  error, 
  onAction, 
  actionText = 'Try Again',
  title = 'Error'
}) => {
  if (!error) return null;
  
  return (
    <div className="error-container">
      <h2>{title}</h2>
      <p>{error}</p>
      {onAction && (
        <Button type="primary" onClick={onAction}>
          {actionText}
        </Button>
      )}
    </div>
  );
};

export default ErrorDisplay;
