export const config = {
  graphqlUrl: import.meta.env.VITE_GRAPHQL_URL,
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
  isDevelopment: import.meta.env.VITE_NODE_ENV === 'development',
};

// 货币配置
export const CURRENCY_SYMBOL = '€';

/**
 * 格式化价格
 * @param price 价格数值
 * @param decimals 小数位数，默认为2
 * @returns 格式化后的价格字符串，包含货币符号
 */
export const formatPrice = (price: number, decimals: number = 2): string => {
  return `${CURRENCY_SYMBOL}${price.toFixed(decimals)}`;
};
