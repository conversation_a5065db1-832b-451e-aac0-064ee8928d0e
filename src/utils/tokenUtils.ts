export function extractTokenFromPath(): string {
  const pathParts = window.location.pathname.split('/');
  
  // Check for both /m/ and /a/ paths
  const mIndex = pathParts.indexOf('m');
  const aIndex = pathParts.indexOf('a');
  
  // Use whichever index is found (m or a)
  const tokenIndex = mIndex !== -1 ? mIndex : aIndex;
  
  if (tokenIndex === -1 || tokenIndex === pathParts.length - 1) {
    throw new Error('Invalid URL format: missing token');
  }
  
  return pathParts[tokenIndex + 1];
}
