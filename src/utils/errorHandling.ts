import { ApolloError } from '@apollo/client';
import { message } from 'antd';

// WhatsApp error codes as specified in the API design
export enum WhatsAppErrorCode {
  INVALID_TOKEN = 'WHATSAPP_INVALID_TOKEN',
  EXPIRED_SESSION = 'WHATSAPP_EXPIRED_SESSION',
  MISSING_TOKEN = 'WHATSAPP_MISSING_TOKEN',
  INVALID_CUSTOMER = 'WHATSAPP_INVALID_CUSTOMER',
}

// WhatsApp error types with messages
export const WHATSAPP_ERROR_TYPES = {
  INVALID_TOKEN: {
    code: 'WHATSAPP_INVALID_TOKEN',
    message: 'Invalid token. Please send a message in WhatsApp to get a new link.',
  },
  EXPIRED_SESSION: {
    code: 'WHATSAPP_EXPIRED_SESSION',
    message: 'Your session has expired. Please send a message in WhatsApp to get a new link.',
  },
  MISSING_TOKEN: {
    code: 'WHATSAPP_MISSING_TOKEN',
    message: 'Authentication required. Please send a message in WhatsApp to get a new link.',
  },
  INVALID_CUSTOMER: {
    code: 'WHATSAPP_INVALID_CUSTOMER',
    message: 'Customer not found. Please send a message in WhatsApp to get a new link.',
  },
};

/**
 * Handle GraphQL errors from WhatsApp API
 * @param error ApolloError object
 * @returns boolean indicating if the error was handled
 */
export function handleWhatsAppGraphQLError(error: ApolloError): boolean {
  if (!error.graphQLErrors || error.graphQLErrors.length === 0) {
    // Check for network errors that might be 401 unauthorized
    if (error.networkError && 'statusCode' in error.networkError && error.networkError.statusCode === 401) {
      return handleWhatsAppHTTPError(401, error.message);
    }
    return false;
  }

  for (const graphQLError of error.graphQLErrors) {
    const code = graphQLError.extensions?.code as string;

    switch (code) {
      case WhatsAppErrorCode.INVALID_TOKEN:
        message.error(WHATSAPP_ERROR_TYPES.INVALID_TOKEN.message);
        return true;

      case WhatsAppErrorCode.EXPIRED_SESSION:
        message.error(WHATSAPP_ERROR_TYPES.EXPIRED_SESSION.message);
        return true;

      case WhatsAppErrorCode.MISSING_TOKEN:
        message.error(WHATSAPP_ERROR_TYPES.MISSING_TOKEN.message);
        return true;

      case WhatsAppErrorCode.INVALID_CUSTOMER:
        message.error(WHATSAPP_ERROR_TYPES.INVALID_CUSTOMER.message);
        return true;
    }
  }

  return false;
}

/**
 * Handle HTTP errors from WhatsApp API
 * @param statusCode HTTP status code
 * @param errorMessage Error message from the server
 * @returns boolean indicating if the error was handled
 */
export function handleWhatsAppHTTPError(statusCode: number, errorMessage?: string): boolean {
  // Parse error message if it's a JSON string
  let errorObj = null;
  if (errorMessage) {
    try {
      if (errorMessage.includes('{"error":')) {
        errorObj = JSON.parse(errorMessage.substring(errorMessage.indexOf('{')));
      }
    } catch (e) {
      console.error('Failed to parse error message:', e);
    }
  }

  // Handle 401 Unauthorized errors
  if (statusCode === 401) {
    if (errorObj?.error === 'Missing or invalid authorization header') {
      message.error(WHATSAPP_ERROR_TYPES.MISSING_TOKEN.message);
      return true;
    }
    // Default to missing token if we can't determine the specific error
    message.error(WHATSAPP_ERROR_TYPES.MISSING_TOKEN.message);
    return true;
  }

  return false;
}
