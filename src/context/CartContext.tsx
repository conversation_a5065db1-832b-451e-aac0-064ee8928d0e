import React, { createContext, useContext, useState, useEffect } from 'react';
import restaurantDataJson from '../data/restaurant.json';

interface MenuItem {
  MenuItemId: string | number;
  Name: string;
  Price: number;
}

interface CartItemVariation {
  _id: string;
  title: string;
  price: number;
  discounted?: number;
}

interface CartItem {
  menuItem: MenuItem;
  quantity: number;
  selectedOptions: {
    optionSetId: string;
    optionIds: string[];
  }[];
  variation?: CartItemVariation; // 更新variation的类型定义
  cartItemId: string;
  specialInstructions?: string;
}

interface IOption {
  _id: string;
  title: string;
  price: number;
  description: string | null;
}

interface CartContextType {
  cart: CartItem[];
  addToCart: (item: Omit<CartItem, 'cartItemId'>) => void;
  removeFromCart: (cartItemId: string) => void;
  updateQuantity: (cartItemId: string, quantity: number) => void;
  clearCart: () => void;
  initializeCart: (sessionData: any) => boolean;
  option_map: Record<string, IOption>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

// Generate a unique ID for cart items
const generateCartItemId = (item: Omit<CartItem, 'cartItemId'>): string => {
  const menuItemId = item.menuItem.MenuItemId;
  const variationId = item.variation?._id || 'no-variation';

  // Sort and stringify selected options to ensure consistent ID generation
  const optionsString = item.selectedOptions
    .map(optSet => {
      return `${optSet.optionSetId}:${[...optSet.optionIds].sort().join(',')}`;
    })
    .sort()
    .join('|');

  return `${menuItemId}-${variationId}-${optionsString}`;
};

export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [option_map, setOptionMap] = useState<Record<string, IOption>>({});

  useEffect(() => {
    // Initialize option_map from restaurant data
    if (restaurantDataJson && restaurantDataJson.restaurant && restaurantDataJson.restaurant.options) {
      const newOptionMap: Record<string, IOption> = {};
      restaurantDataJson.restaurant.options.forEach((option: IOption) => {
        newOptionMap[option._id] = option;
      });
      setOptionMap(newOptionMap);
    }
  }, []);

  const addToCart = (item: Omit<CartItem, 'cartItemId'>) => {
    setCart(prevCart => {
      // 确保variation的所有必要字段都被保存
      const itemToAdd = {
        ...item,
        variation: item.variation
          ? {
              _id: item.variation._id,
              title: item.variation.title,
              price: item.variation.price,
              discounted: item.variation.discounted,
            }
          : undefined,
      };

      // Generate a unique ID for the cart item
      const cartItemId = generateCartItemId(itemToAdd);

      // Check if the item already exists
      const existingItemIndex = prevCart.findIndex(cartItem => cartItem.cartItemId === cartItemId);

      if (existingItemIndex >= 0) {
        // Log the existing item for debugging
        console.log('Existing item:', prevCart[existingItemIndex]);

        return prevCart.map((cartItem, index) =>
          index === existingItemIndex ? { ...cartItem, quantity: cartItem.quantity + itemToAdd.quantity } : cartItem
        );
      }

      // Log the new item for debugging
      console.log('Adding new item:', { ...itemToAdd, cartItemId });

      return [...prevCart, { ...itemToAdd, cartItemId }];
    });
  };

  const removeFromCart = (cartItemId: string) => {
    setCart(prevCart => prevCart.filter(item => item.cartItemId !== cartItemId));
  };

  const updateQuantity = (cartItemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(cartItemId);
      return;
    }

    setCart(prevCart => prevCart.map(item => (item.cartItemId === cartItemId ? { ...item, quantity } : item)));
  };

  const clearCart = () => {
    setCart([]);
  };

  // Initialize cart with session data from WhatsApp menu link
  const initializeCart = (sessionData: any) => {
    if (!sessionData || !sessionData.orderInput || !Array.isArray(sessionData.orderInput)) {
      console.error('Invalid session data format for cart initialization');
      return false;
    }

    // Clear existing cart first
    clearCart();

    // Process each item from the session data
    const cartItems = sessionData.orderInput
      .map((item: any) => {
        // Find the menu item in restaurant data
        const menuItemId = item.food;
        let menuItem = null;

        // Search through all categories and foods to find the matching menu item
        if (restaurantDataJson && restaurantDataJson.restaurant && restaurantDataJson.restaurant.categories) {
          for (const category of restaurantDataJson.restaurant.categories) {
            if (category.foods) {
              // Match by _id in restaurant.json
              const foundItem = category.foods.find((food: any) => String(food._id) === String(menuItemId));
              if (foundItem) {
                menuItem = {
                  MenuItemId: foundItem._id,
                  Name: foundItem.title,
                  Price: foundItem.price || 0,
                };
                break;
              }
            }
          }
        }

        if (!menuItem) {
          console.error(`Menu item not found for ID: ${menuItemId}`);
          return null;
        }

        // Find variation if specified
        let variation = undefined;
        if (item.variation) {
          // Search for the variation in the menu item
          if (restaurantDataJson && restaurantDataJson.restaurant && restaurantDataJson.restaurant.categories) {
            for (const category of restaurantDataJson.restaurant.categories) {
              if (category.foods) {
                // Match by _id in restaurant.json
                const food = category.foods.find((f: any) => String(f._id) === String(menuItemId));
                if (food && food.variations) {
                  const foundVariation = food.variations.find((v: any) => v._id === item.variation);
                  if (foundVariation) {
                    variation = {
                      _id: foundVariation._id,
                      title: foundVariation.title,
                      price: foundVariation.price,
                      discounted: foundVariation.discounted,
                    };
                  }
                }
              }
            }
          }
        }

        // Convert addons to selectedOptions format
        const selectedOptions = item.addons
          ? item.addons.map((addon: any) => ({
              optionSetId: addon._id,
              optionIds: addon.options || [],
            }))
          : [];

        // Create cart item
        const cartItem: Omit<CartItem, 'cartItemId'> = {
          menuItem,
          quantity: item.quantity,
          selectedOptions,
          variation,
          specialInstructions: item.specialInstructions,
        };

        return cartItem;
      })
      .filter(Boolean); // Remove any null items

    // Add each item to the cart
    cartItems.forEach((item: any) => {
      if (item) {
        addToCart(item);
      }
    });

    console.log('Cart initialized with session data:', cartItems);
    return cartItems.length > 0;
  };

  return (
    <CartContext.Provider value={{ cart, addToCart, removeFromCart, updateQuantity, clearCart, initializeCart, option_map }}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
