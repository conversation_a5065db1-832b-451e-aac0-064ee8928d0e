import { ApolloClient, InMemoryCache, createHttpLink, ApolloLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import { extractTokenFromPath } from './utils/tokenUtils';

const httpLink = createHttpLink({
  uri: `${import.meta.env.VITE_API_URL}${import.meta.env.VITE_GRAPHQL_ENDPOINT}`,
});

const authLink = setContext((_, { headers }) => {
  // Extract token from URL path instead of localStorage
  let token = '';
  try {
    token = extractTokenFromPath();
  } catch (error) {
    console.error('Failed to extract token from path:', error);
  }

  return {
    headers: {
      ...headers,
      // Use X-WhatsAppW-Token header as per specification
      'X-WhatsAppW-Token': token,
    },
  };
});

// Error handling link
const errorLink = onError(({ graphQLErrors, networkError }) => {
  // Log GraphQL errors
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path }) => {
      console.error(`[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`);
    });
  }

  // Log network errors
  if (networkError) {
    console.error(`[Network error]: ${networkError}`);
  }
});

export const client = new ApolloClient({
  link: ApolloLink.from([errorLink, authLink.concat(httpLink)]),
  cache: new InMemoryCache(),
});
