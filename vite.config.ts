import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '');

  return {
    plugins: [react()],
    server: {
      port: 3008,
      open: true,
      host: true,
      strictPort: true,
    },
    build: {
      outDir: 'dist',
      sourcemap: true,
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },
    base: '/',
    define: {
      // 定义全局环境变量
      'import.meta.env.VITE_API_URL': JSON.stringify(env.VITE_API_URL),
      'import.meta.env.VITE_GRAPHQL_ENDPOINT': JSON.stringify(env.VITE_GRAPHQL_ENDPOINT),
      'import.meta.env.VITE_AUTH_TOKEN_KEY': JSON.stringify(env.VITE_AUTH_TOKEN_KEY || 'token'),
    },
  };
});
