# Restaurant Ordering Website

This project is a React-based restaurant ordering website that interacts with a GraphQL server using Apollo Client and MongoDB. It allows users to view restaurant information, browse menus, customize food items, manage a shopping cart, and handle the checkout process.

## Features

- Display restaurant information and menus
- Customize food items
- Manage shopping cart
- Checkout process with form submission

## Technologies Used

- React
- Apollo Client
- GraphQL
- MongoDB
- TypeScript

## Project Structure

```
restaurant-ordering-website
├── public
│   ├── index.html
│   └── favicon.ico
├── src
│   ├── components
│   ├── graphql
│   ├── pages
│   ├── types
│   ├── App.tsx
│   ├── index.tsx
├── package.json
├── tsconfig.json
├── .env
└── README.md
```

## Setup Instructions

1. Clone the repository:

   ```
   git clone <repository-url>
   ```

2. Navigate to the project directory:

   ```
   cd restaurant-ordering-website
   ```

3. Install dependencies:

   ```
   npm install
   ```

4. Create a `.env` file in the root directory and add your GraphQL server URL:

   ```
   REACT_APP_GRAPHQL_URL=<your-graphql-server-url>
   ```

5. Start the development server:
   ```
   npm start
   ```

## Usage

- Visit the home page to view restaurant information.
- Navigate to the menu page to browse available food items.
- Customize items and add them to your cart.
- Proceed to checkout to complete your order.

## License

This project is licensed under the MIT License.
